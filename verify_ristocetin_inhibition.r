# Script to verify ristocetin inhibition after parameter adjustments

# Load required libraries
library(deSolve)
library(ggplot2)
library(dplyr)

# Source the main model functions (simplified version)
source("ibrutinib_comprehensive_model_with_interactions.r")

# Test ristocetin inhibition at different ibrutinib concentrations
ibrutinib_concentrations <- c(0, 41.14, 82.28, 123.41, 164.55, 246.83)  # 0-840mg doses
doses <- c(0, 140, 280, 420, 560, 840)

# Ristocetin parameters
ristocetin_params <- list(
  concentration = 1.0,
  pathway_type = "ristocetin",
  EC50 = 0.75,
  hill_coef = 1.3,
  pathway_contribution = 0.8,
  TxA2_generation_capacity = 0.1,
  ADP_release_efficiency = 0.2,
  thrombin_generation = 0.1,
  GPVI_crosstalk = 0.1,
  PAR_crosstalk = 0.05
)

# Calculate BTK inhibition for ristocetin pathway
dose_response <- function(conc, IC50, hill_coef, max_inhib) {
  if (conc == 0) return(1.0)
  base_response <- (conc^hill_coef) / (IC50^hill_coef + conc^hill_coef)
  inhibition <- max_inhib * base_response
  return(1 - inhibition)
}

cat("\n=== RISTOCETIN PATHWAY INHIBITION ANALYSIS ===\n")
cat("Updated parameters: IC50 = 2000 nM, Max inhibition = 5%\n")
cat("Reduced cross-talk: GPVI = 0.1, ADP release = 0.2, TxA2 generation = 0.1\n\n")

results <- data.frame(
  dose_mg = doses,
  ibrutinib_conc_nM = ibrutinib_concentrations,
  btk_inhibition_factor = numeric(length(doses)),
  percent_inhibition = numeric(length(doses))
)

for (i in 1:length(ibrutinib_concentrations)) {
  conc <- ibrutinib_concentrations[i]
  
  # Calculate BTK inhibition factor for ristocetin (IC50=2000 nM, max=5%)
  btk_factor <- dose_response(conc, 2000, 1.0, 0.05)
  
  # Calculate percent inhibition
  percent_inhib <- (1 - btk_factor) * 100
  
  results$btk_inhibition_factor[i] <- btk_factor
  results$percent_inhibition[i] <- percent_inhib
}

print(results)

cat("\n=== KEY FINDINGS ===\n")
cat(sprintf("Ristocetin inhibition at 420mg dose: %.1f%%\n", results$percent_inhibition[4]))
cat(sprintf("Maximum inhibition at 840mg dose: %.1f%%\n", max(results$percent_inhibition)))
cat("\nComparison with literature:\n")
cat("- Clinical observations: No significant effect on ristocetin aggregation\n")
cat("- VWF/GPIb pathway: Largely BTK-independent\n")
cat("- Model prediction: Minimal inhibition due to adjusted parameters\n")

if (results$percent_inhibition[4] < 10) {
  cat("\n✓ SUCCESS: Model now matches clinical observations (<10% inhibition)\n")
} else {
  cat("\n✗ NEEDS ADJUSTMENT: Still overestimating inhibition\n")
}