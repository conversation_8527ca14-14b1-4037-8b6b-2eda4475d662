# Quick script to check ristocetin results

# Load required libraries
library(dplyr)

# Read the CSV output if it exists
if (file.exists("comprehensive_results_with_interactions.csv")) {
  results <- read.csv("comprehensive_results_with_interactions.csv")
  
  # Filter for ristocetin at 420mg dose
  ristocetin_420 <- results %>%
    filter(agonist_name == "Ristocetin" & dose_mg == 420)
  
  if (nrow(ristocetin_420) > 0) {
    cat("\n=== RISTOCETIN RESULTS AT 420MG ===\n")
    print(ristocetin_420)
    
    # Calculate inhibition percentage
    control_ristocetin <- results %>%
      filter(agonist_name == "Ristocetin" & dose_mg == 0)
    
    if (nrow(control_ristocetin) > 0) {
      inhibition_pct <- ((control_ristocetin$peak_agg - ristocetin_420$peak_agg) / control_ristocetin$peak_agg) * 100
      cat("\nRistocetin inhibition at 420mg:", round(inhibition_pct, 1), "%\n")
    }
  } else {
    cat("No ristocetin data found at 420mg\n")
  }
} else {
  cat("Results file not found\n")
}

# Also check if there are any other result files
cat("\nAvailable files:\n")
print(list.files(pattern = "*.csv"))