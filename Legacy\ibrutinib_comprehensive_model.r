# =============================================================================
# COMPREHENSIVE IBRUTINIB PLATELET AGGREGATION MODEL WITH ADDITIONAL AGONISTS
# =============================================================================
# This comprehensive model incorporates additional agonists from CSV data while
# maintaining the existing model structure and excluding bleeding risk assessment
# =============================================================================

# Required packages
library(deSolve)    # For solving ODEs
library(ggplot2)    # For visualization
library(dplyr)      # For data manipulation
library(tidyr)      # For data reshaping
library(patchwork)  # For combining plots
library(RColorBrewer) # For better color palettes
library(svglite)
library(readr)      # For CSV reading

# =============================================================================
# PART 1: DATA IMPORT AND VALIDATION
# =============================================================================

# Function to import and validate CSV data
import_agonist_data <- function(csv_file) {
  tryCatch({
    # Read CSV file
    agonist_data <- read_csv(csv_file, show_col_types = FALSE)
    
    # Validate required columns
    required_cols <- c("Agonist", "Concentration Range", "Primary Pathway", "Secondary Pathways")
    missing_cols <- setdiff(required_cols, colnames(agonist_data))
    
    if (length(missing_cols) > 0) {
      stop(paste("Missing required columns:", paste(missing_cols, collapse = ", ")))
    }
    
    # Clean and standardize data
    agonist_data <- agonist_data %>%
      mutate(
        Agonist = trimws(Agonist),
        `Primary Pathway` = trimws(`Primary Pathway`),
        `Secondary Pathways` = trimws(`Secondary Pathways`)
      ) %>%
      filter(!is.na(Agonist) & Agonist != "")
    
    cat("Successfully imported", nrow(agonist_data), "agonists from CSV\n")
    return(agonist_data)
    
  }, error = function(e) {
    stop(paste("Error importing CSV file:", e$message))
  })
}

# Import agonist data
agonist_csv_file <- "Agonist-ConcentrationRange-PrimaryPathway-SecondaryPathways.csv"
agonist_data <- import_agonist_data(agonist_csv_file)

# Display imported data
cat("\n=== IMPORTED AGONIST DATA ===\n")
print(agonist_data)

# =============================================================================
# PART 2: PHARMACOKINETIC (PK) MODEL - UNCHANGED
# =============================================================================

# Function to run PK simulation for a specific dose (including 0 mg control)
run_pk_simulation <- function(dose_mg) {
  pk_params <- c(
    ka1 = 0.451,        # First-order absorption rate (h^-1)
    ke = 0.1,           # Elimination rate (h^-1)
    Vcentral = 1000,    # Central compartment volume (L)
    F = 0.06            # Bioavailability
  )
  
  # Handle control case (no drug administration)
  if (dose_mg == 0) {
    # For control, create zero concentration profile
    sim_times <- seq(0, 14*24, by = 0.1)
    pk_sim_results <- data.frame(
      time = sim_times, 
      Cp = rep(0, length(sim_times)),
      Cp_nM = rep(0, length(sim_times)),
      dose_mg = 0
    )
    return(pk_sim_results)
  }
  
  # Standard PK simulation for non-zero doses
  state_init <- c(
    Agut = dose_mg * 1000 * unname(pk_params["F"]),
    Acentral = 0
  )
  
  # Pharmacokinetic model function
  pk_model <- function(time, state, parameters) {
    with(as.list(c(state, parameters)), {
      # Differential equations for PK model
      dAgut_dt <- - ka1 * state[1]
      dAcentral_dt <- ka1 * state[1] - ke * state[2]
      
      # Calculate plasma concentration
      Cp <- Acentral / Vcentral
      
      # Return derivatives
      return(list(c(dAgut_dt, dAcentral_dt), Cp = Cp))
    })
  }
  
  # Define dosing regimen once daily for 14 days
  dose_times <- seq(24, 13*24, by = 24)  # Every 24 hours
  
  injectevents <- data.frame(var = "Agut",
                             time = dose_times,
                             value = dose_mg*1000*unname(pk_params["F"]),
                             method = "add")
  
  # Initialize simulation
  sim_times <- seq(0, 14*24, by = 0.1)  # 14 days with 0.1h resolution
  out <- ode(func = pk_model, times = sim_times, y = state_init,
             parms = pk_params,
             events = list(data = injectevents))
  
  pk_sim_results <- data.frame(time = sim_times, Cp = numeric(length(sim_times)))
  pk_sim_results$Cp = out[, "Cp"]
  
  # Convert concentration from μg/L to nM (MW of ibrutinib = 440.5 g/mol)
  pk_sim_results$Cp_nM <- pk_sim_results$Cp / 440.5 * 1000
  pk_sim_results$dose_mg <- dose_mg
  
  return(pk_sim_results)
}

# Define doses to simulate including control (0 mg)
doses <- c(0, 140, 280, 420, 560, 840)  # Added 0 mg as control

# Run PK simulations for each dose including control
pk_results_list <- lapply(doses, run_pk_simulation)
pk_all_results <- bind_rows(pk_results_list)

# =============================================================================
# PART 3: COMPREHENSIVE AGONIST PARAMETER MAPPING
# =============================================================================

# Function to parse concentration ranges and extract numeric values
parse_concentration_range <- function(conc_range, agonist_name) {
  tryCatch({
    # Remove extra spaces and standardize
    conc_range <- trimws(conc_range)
    
    # Extract numeric values using regex
    if (grepl("μM", conc_range)) {
      # Extract range for μM concentrations
      nums <- as.numeric(unlist(regmatches(conc_range, gregexpr("[0-9.]+", conc_range))))
      return(list(min = nums[1], max = nums[length(nums)], unit = "μM"))
    } else if (grepl("μg/mL|mg/mL", conc_range)) {
      # Extract range for μg/mL or mg/mL concentrations
      nums <- as.numeric(unlist(regmatches(conc_range, gregexpr("[0-9.]+", conc_range))))
      unit <- ifelse(grepl("mg/mL", conc_range), "mg/mL", "μg/mL")
      return(list(min = nums[1], max = nums[length(nums)], unit = unit))
    } else if (grepl("U/mL", conc_range)) {
      # Extract range for U/mL concentrations
      nums <- as.numeric(unlist(regmatches(conc_range, gregexpr("[0-9.]+", conc_range))))
      return(list(min = nums[1], max = nums[length(nums)], unit = "U/mL"))
    } else if (grepl("mM", conc_range)) {
      # Extract range for mM concentrations
      nums <- as.numeric(unlist(regmatches(conc_range, gregexpr("[0-9.]+", conc_range))))
      return(list(min = nums[1], max = nums[length(nums)], unit = "mM"))
    } else {
      warning(paste("Unknown concentration unit for", agonist_name, ":", conc_range))
      return(list(min = 1.0, max = 5.0, unit = "unknown"))
    }
  }, error = function(e) {
    warning(paste("Error parsing concentration for", agonist_name, ":", e$message))
    return(list(min = 1.0, max = 5.0, unit = "unknown"))
  })
}

# Function to map agonists to pathway parameters
map_agonist_parameters <- function(agonist_data) {
  agonist_params <- list()
  
  for (i in 1:nrow(agonist_data)) {
    agonist_name <- agonist_data$Agonist[i]
    primary_pathway <- agonist_data$`Primary Pathway`[i]
    conc_info <- parse_concentration_range(agonist_data$`Concentration Range`[i], agonist_name)
    
    # Use middle of concentration range as default
    default_conc <- (conc_info$min + conc_info$max) / 2
    
    # Map to pathway parameters based on primary pathway
    if (grepl("P2Y", primary_pathway, ignore.case = TRUE)) {
      # ADP/P2Y pathway
      agonist_params[[agonist_name]] <- list(
        pathway_type = "ADP",
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 1.0,  # Default EC50 for P2Y activation
        hill_coef = 1.2,
        BTK_dependence = 0.3,
        pathway_contribution = 0.5
      )
    } else if (grepl("GPVI|α₂β₁", primary_pathway, ignore.case = TRUE)) {
      # Collagen/GPVI pathway
      agonist_params[[agonist_name]] <- list(
        pathway_type = "collagen",
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 1.0,  # Default EC50 for GPVI activation
        hill_coef = 1.5,
        BTK_dependence = 1.0,
        pathway_contribution = 0.7
      )
    } else if (grepl("PAR", primary_pathway, ignore.case = TRUE)) {
      # Thrombin/PAR pathway
      agonist_params[[agonist_name]] <- list(
        pathway_type = "thrombin",
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 0.05,  # Default EC50 for PAR activation
        hill_coef = 2.0,
        BTK_dependence = 0.1,
        pathway_contribution = 0.9
      )
    } else if (grepl("COX|TxA₂", primary_pathway, ignore.case = TRUE)) {
      # Arachidonic Acid/COX pathway (similar to ADP with TxA2 amplification)
      agonist_params[[agonist_name]] <- list(
        pathway_type = "ADP",  # Use ADP pathway as base
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 0.5,  # Lower EC50 due to amplification
        hill_coef = 1.8,
        BTK_dependence = 0.2,
        pathway_contribution = 0.6
      )
    } else if (grepl("α₂A-adrenergic|Gi", primary_pathway, ignore.case = TRUE)) {
      # Epinephrine/α2A-adrenergic pathway (weak, Gi-mediated)
      agonist_params[[agonist_name]] <- list(
        pathway_type = "ADP",  # Use ADP pathway as base
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 5.0,  # Higher EC50 (weaker response)
        hill_coef = 1.0,
        BTK_dependence = 0.05,  # Minimal BTK dependence
        pathway_contribution = 0.2
      )
    } else if (grepl("VWF|GPIb", primary_pathway, ignore.case = TRUE)) {
      # Ristocetin/VWF pathway (mechanical activation, similar to collagen)
      agonist_params[[agonist_name]] <- list(
        pathway_type = "collagen",  # Use collagen pathway as base
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 0.8,
        hill_coef = 1.3,
        BTK_dependence = 0.4,  # Moderate BTK dependence
        pathway_contribution = 0.5
      )
    } else {
      # Default parameters for unknown pathways
      warning(paste("Unknown pathway for", agonist_name, ":", primary_pathway))
      agonist_params[[agonist_name]] <- list(
        pathway_type = "ADP",  # Default to ADP pathway
        concentration = default_conc,
        unit = conc_info$unit,
        EC50 = 1.0,
        hill_coef = 1.2,
        BTK_dependence = 0.3,
        pathway_contribution = 0.5
      )
    }
  }
  
  return(agonist_params)
}

# Generate agonist parameters
agonist_params <- map_agonist_parameters(agonist_data)

# Display mapped parameters
cat("\n=== MAPPED AGONIST PARAMETERS ===\n")
for (agonist in names(agonist_params)) {
  cat(sprintf("%s: %s pathway, %.2f %s\n", 
              agonist, 
              agonist_params[[agonist]]$pathway_type,
              agonist_params[[agonist]]$concentration,
              agonist_params[[agonist]]$unit))
}

# =============================================================================
# PART 4: COMPREHENSIVE STIMULI FUNCTION
# =============================================================================

# Comprehensive stimuli function to handle all agonists
get_comprehensive_stimuli <- function(time, parameters, agonist_name, agonist_params) {
  with(as.list(parameters), {
    # Initialize all stimuli to zero
    collagen <- 0
    ADP <- 0
    thrombin <- 0
    
    # Define when stimulation happens (8 AM each day)
    stim_times <- seq(8, 14*24, by = 24)
    
    # Check if current time is within activation window
    for (stim_time in stim_times) {
      if (time >= stim_time && time < (stim_time + 0.5)) {
        # Smooth sigmoid activation
        relative_time <- (time - stim_time) / 0.5
        scale_factor <- 0.5 * (tanh(10 * (relative_time - 0.5)) + 1)
        
        # Get agonist-specific parameters
        agonist_conc <- agonist_params[[agonist_name]]$concentration
        pathway_type <- agonist_params[[agonist_name]]$pathway_type
        
        # Activate appropriate pathway based on agonist
        if (pathway_type == "collagen") {
          collagen <- scale_factor * agonist_conc
        } else if (pathway_type == "ADP") {
          ADP <- scale_factor * agonist_conc
        } else if (pathway_type == "thrombin") {
          thrombin <- scale_factor * agonist_conc
        }
      }
    }
    
    return(list(collagen = collagen, ADP = ADP, thrombin = thrombin))
  })
}

# =============================================================================
# PART 5: COMPREHENSIVE PHARMACODYNAMIC MODEL
# =============================================================================

# Comprehensive PD model to handle all agonists
comprehensive_pd_model <- function(time, state, parameters, ibrutinib_conc, agonist_name, agonist_params) {
  with(as.list(c(state, parameters)), {
    # Extract current ibrutinib concentration
    t_idx <- max(1, which.min(abs(time - ibrutinib_conc$time)))
    C_ibr <- ibrutinib_conc$Cp_nM[t_idx]
    
    # Get current stimulus levels for this agonist
    stimuli <- get_comprehensive_stimuli(time, parameters, agonist_name, agonist_params)
    collagen <- stimuli$collagen
    ADP <- stimuli$ADP
    thrombin <- stimuli$thrombin
    
    # Safety functions
    safeguard <- function(x, min_val = 1e-10, max_val = 1e10) {
      pmax(pmin(x, max_val), min_val)
    }
    
    hill_function <- function(x, EC50, n) {
      x_safe <- safeguard(x)
      EC50_safe <- safeguard(EC50)
      result <- (x_safe/EC50_safe)^n / (1 + (x_safe/EC50_safe)^n)
      safeguard(result, min_val = 0, max_val = 1)
    }
    
    # Dose-response function
    dose_response <- function(conc, IC50, hill_coef, max_inhib) {
      if (conc == 0) return(1.0)
      conc_safe <- safeguard(conc)
      base_response <- (conc_safe^hill_coef) / (IC50^hill_coef + conc_safe^hill_coef)
      inhibition <- max_inhib * base_response
      return(1 - inhibition)
    }
    
    # Get agonist-specific parameters
    agonist_info <- agonist_params[[agonist_name]]
    pathway_type <- agonist_info$pathway_type
    
    # BTK dynamics with pathway-specific parameters
    if (pathway_type == "collagen") {
      Ki_eff <- Ki_collagen
      max_inhib_BTK <- max_inhib_BTK_collagen
      hill_BTK <- hill_BTK_collagen
    } else if (pathway_type == "ADP") {
      Ki_eff <- Ki_ADP
      max_inhib_BTK <- max_inhib_BTK_ADP
      hill_BTK <- hill_BTK_ADP
    } else {
      Ki_eff <- Ki_thrombin
      max_inhib_BTK <- max_inhib_BTK_thrombin
      hill_BTK <- hill_BTK_thrombin
    }
    
    # BTK dynamics
    BTK_free_safe <- safeguard(BTK_free)
    ibr_effect <- dose_response(C_ibr, Ki_eff, hill_BTK, max_inhib_BTK)
    
    dBTK_free_dt <- k_syn - k_inact * (1 - ibr_effect) * BTK_free_safe - k_deg * BTK_free_safe
    dBTK_bound_dt <- k_inact * (1 - ibr_effect) * BTK_free_safe - k_deg * BTK_bound
    
    # Pathway activation with agonist-specific EC50
    agonist_EC50 <- agonist_info$EC50
    agonist_hill <- agonist_info$hill_coef
    
    # GPVI pathway
    GPVI_activation <- hill_function(collagen, agonist_EC50, agonist_hill)
    BTK_effect_GPVI <- hill_function(BTK_free_safe, EC50_BTK, n_BTK)
    GPVI_signaling <- GPVI_activation * BTK_effect_GPVI
    
    # P2Y pathway
    P2Y_activation <- hill_function(ADP, agonist_EC50, agonist_hill)
    P2Y_BTK_effect <- hill_function(BTK_free_safe, EC50_BTK_P2Y, 1)
    P2Y_signaling <- P2Y_activation * (R_BTK_P2Y * P2Y_BTK_effect + (1 - R_BTK_P2Y))
    
    # PAR pathway
    PAR_activation <- hill_function(thrombin, agonist_EC50, agonist_hill)
    PAR_BTK_effect <- hill_function(BTK_free_safe, EC50_BTK_PAR, 1)
    PAR_signaling <- PAR_activation * (R_BTK_PAR * PAR_BTK_effect + (1 - R_BTK_PAR))
    
    # Tec kinase contribution
    Tec_inhibition <- dose_response(C_ibr, Ki_Tec, hill_Tec, max_inhib_Tec)
    
    if (pathway_type == "collagen") {
      Tec_effect <- Tec_inhibition * Tec_contribution * GPVI_activation
      PLC_activation <- k_PLC_act * safeguard(agonist_info$pathway_contribution * GPVI_signaling + Tec_effect, min_val = 0, max_val = 1)
    } else if (pathway_type == "ADP") {
      Tec_effect <- Tec_inhibition * Tec_contribution * P2Y_activation * 0.5
      PLC_activation <- k_PLC_act * safeguard(agonist_info$pathway_contribution * P2Y_signaling + Tec_effect, min_val = 0, max_val = 1)
    } else {
      Tec_effect <- Tec_inhibition * Tec_contribution * PAR_activation * 0.3
      PLC_activation <- k_PLC_act * safeguard(agonist_info$pathway_contribution * PAR_signaling + Tec_effect, min_val = 0, max_val = 1)
    }
    
    dPLCgamma2_act_dt <- PLC_activation - k_PLC_deact * PLCgamma2_act
    
    # Downstream signaling (unchanged)
    Ca_effect <- hill_function(PLCgamma2_act, EC50_Ca, n_Ca)
    dCa_dt <- k_Ca_in * Ca_effect - k_Ca_out * Ca
    
    Int_effect <- hill_function(Ca, EC50_int, n_int)
    dIntegrin_act_dt <- k_int_act * Int_effect - k_int_deact * Integrin_act
    
    dPsel_exp_dt <- k_Psel_exp * safeguard(Ca, min_val = 0, max_val = 1) - k_Psel_int * Psel_exp
    
    # Aggregation
    Agg_safe <- safeguard(Agg, min_val = 0, max_val = 0.9999)
    dAgg_dt <- k_agg * safeguard(Integrin_act, min_val = 0, max_val = 1) * (1 - Agg_safe) * 
      (1 + k_coop * Agg_safe) - k_disagg * Agg_safe
    
    return(list(c(
      dBTK_free_dt, dBTK_bound_dt, dPLCgamma2_act_dt, 
      dCa_dt, dIntegrin_act_dt, dPsel_exp_dt, dAgg_dt
    ), GPVI_act = GPVI_activation, P2Y_act = P2Y_activation, PAR_act = PAR_activation,
    Collagen = collagen, ADP = ADP, Thrombin = thrombin, Ibr_conc = C_ibr))
  })
}

# =============================================================================
# PART 6: MODEL PARAMETERS (COMPREHENSIVE)
# =============================================================================

# Comprehensive PD parameters
pd_params <- c(
  # BTK parameters
  k_syn = 0.05,
  k_deg = 0.035,
  k_inact = 3.0,
  
  # Ibrutinib dose-response parameters
  Ki_collagen = 150,
  max_inhib_BTK_collagen = 0.98,
  hill_BTK_collagen = 1.5,
  
  Ki_ADP = 400,
  max_inhib_BTK_ADP = 0.55,
  hill_BTK_ADP = 1.2,
  
  Ki_thrombin = 700,
  max_inhib_BTK_thrombin = 0.35,
  hill_BTK_thrombin = 1.0,
  
  # Tec kinase parameters
  Ki_Tec = 10.0,
  hill_Tec = 1.2,
  max_inhib_Tec = 0.9,
  Tec_contribution = 0.3,
  
  # Pathway parameters
  R_GPVI = 0.7,
  R_P2Y = 0.5,
  R_PAR = 0.9,
  R_BTK_P2Y = 0.3,
  R_BTK_PAR = 0.1,
  EC50_BTK_P2Y = 1.0,
  EC50_BTK_PAR = 1.0,
  
  # PLCγ2 parameters
  k_PLC_act = 10.0,
  k_PLC_deact = 20.0,
  n_BTK = 1.5,
  EC50_BTK = 2.0,
  
  # Calcium parameters
  k_Ca_in = 60.0,
  k_Ca_out = 120.0,
  n_Ca = 2.0,
  EC50_Ca = 0.3,
  
  # Integrin parameters
  k_int_act = 30.0,
  k_int_deact = 15.0,
  n_int = 1.5,
  EC50_int = 0.5,
  
  # P-selectin parameters
  k_Psel_exp = 24.0,
  k_Psel_int = 6.0,
  
  # Aggregation parameters
  k_agg = 45.0,
  k_disagg = 1.0,
  k_coop = 2.0
)

# Initial conditions
pd_state_init <- c(
  BTK_free = 5.0,
  BTK_bound = 0.0,
  PLCgamma2_act = 0.0,
  Ca = 0.0,
  Integrin_act = 0.0,
  Psel_exp = 0.0,
  Agg = 0.0
)

# =============================================================================
# PART 7: COMPREHENSIVE SIMULATION EXECUTION
# =============================================================================

# Define simulation time points
pd_sim_times <- seq(0, 14*24, by = 0.1)

# Get list of all agonists to simulate
agonist_names <- names(agonist_params)

# Function to run comprehensive PD simulation
run_comprehensive_pd_simulation <- function(dose_mg, agonist_name) {
  # Get PK profile for this dose
  pk_data <- pk_results_list[[which(doses == dose_mg)]]
  
  # Run PD model simulation
  pd_out <- ode(
    y = pd_state_init,
    times = pd_sim_times,
    func = comprehensive_pd_model,
    parms = pd_params,
    ibrutinib_conc = pk_data,
    agonist_name = agonist_name,
    agonist_params = agonist_params,
    method = "lsoda",
    rtol = 1e-2,
    atol = 1e-2,
    hmin = 1e-10,
    maxsteps = 50000
  )
  
  # Convert to data frame
  pd_results <- as.data.frame(pd_out)
  names_vector <- c("time", "BTK_free", "BTK_bound", "PLCgamma2_act", 
                    "Ca", "Integrin_act", "Psel_exp", "Agg")
  colnames(pd_results)[1:length(names_vector)] <- names_vector
  
  # Extract additional data
  pd_results$GPVI_activation <- pd_out[,"GPVI_act"]
  pd_results$P2Y_activation <- pd_out[,"P2Y_act"]
  pd_results$PAR_activation <- pd_out[,"PAR_act"]
  pd_results$Collagen <- pd_out[,"Collagen"]
  pd_results$ADP <- pd_out[,"ADP"]
  pd_results$Thrombin <- pd_out[,"Thrombin"]
  pd_results$Ibr_conc <- pd_out[,"Ibr_conc"]
  
  # Add metadata
  pd_results$agonist_name <- agonist_name
  pd_results$dose_mg <- dose_mg
  pd_results$treatment_group <- ifelse(dose_mg == 0, "Control", paste0(dose_mg, " mg"))
  
  return(pd_results)
}

# Run simulations for all combinations
cat("\n=== RUNNING COMPREHENSIVE SIMULATIONS ===\n")
all_comprehensive_results <- list()

for (agonist in agonist_names) {
  cat(sprintf("Simulating %s...\n", agonist))
  agonist_results <- list()
  
  for (dose in doses) {
    agonist_results[[as.character(dose)]] <- run_comprehensive_pd_simulation(dose, agonist)
  }
  
  all_comprehensive_results[[agonist]] <- agonist_results
}

# Combine all results
pd_comprehensive_results <- bind_rows(lapply(all_comprehensive_results, function(agonist_list) {
  bind_rows(agonist_list)
}))

cat("Comprehensive simulations completed successfully!\n")

# =============================================================================
# PART 8: COMPREHENSIVE DATA ANALYSIS
# =============================================================================

# Extract peak responses for all agonists
extract_comprehensive_peak_responses <- function(data) {
  grouped_data <- data %>%
    mutate(day = floor(time/24) + 1) %>%
    group_by(agonist_name, dose_mg, day, treatment_group) %>%
    summarize(
      peak_agg = max(Agg), 
      peak_integrin = max(Integrin_act),
      peak_plc = max(PLCgamma2_act),
      avg_BTK_free = mean(BTK_free),
      max_ibr_conc = max(Ibr_conc),
      .groups = 'drop'
    )
  
  return(grouped_data)
}

# Get comprehensive peak responses
comprehensive_peak_responses <- extract_comprehensive_peak_responses(pd_comprehensive_results)

# Calculate inhibition relative to control for each agonist
calculate_comprehensive_inhibition <- function() {
  day14_data <- comprehensive_peak_responses %>%
    filter(day == 14)
  
  # Get control responses for each agonist
  control_responses <- day14_data %>%
    filter(dose_mg == 0) %>%
    select(agonist_name, control_agg = peak_agg)
  
  # Calculate percent inhibition
  inhibition_data <- day14_data %>%
    left_join(control_responses, by = "agonist_name") %>%
    mutate(percent_inhibition = 100 * (1 - peak_agg/control_agg)) %>%
    select(agonist_name, dose_mg, treatment_group, percent_inhibition, peak_agg, control_agg)
  
  return(inhibition_data)
}

comprehensive_inhibition_data <- calculate_comprehensive_inhibition()

# =============================================================================
# PART 9: COMPREHENSIVE VISUALIZATION
# =============================================================================

# 1. Dose-response curves for all agonists
plot_comprehensive_dose_response <- ggplot(
  comprehensive_peak_responses %>% filter(day == 14), 
  aes(x = dose_mg, y = peak_agg, color = agonist_name, group = agonist_name)
) +
  geom_point(size = 2) +
  geom_line(linewidth = 1) +
  labs(
    title = "Comprehensive Platelet Aggregation Model: All Agonists",
    subtitle = "Day 14 responses across dose range",
    x = "Ibrutinib Dose (mg)",
    y = "Peak Aggregation Response",
    color = "Agonist"
  ) +
  scale_color_brewer(palette = "Set3") +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 14, face = "bold"),
    axis.title = element_text(size = 12),
    panel.grid.minor = element_blank()
  )

# Save dose-response plot as PNG
png("comprehensive_dose_response.png", width = 1200, height = 800, res = 150)
print(plot_comprehensive_dose_response)
dev.off()

# 2. Percent inhibition comparison
plot_comprehensive_inhibition <- ggplot(
  comprehensive_inhibition_data %>% filter(dose_mg > 0), 
  aes(x = dose_mg, y = percent_inhibition, color = agonist_name, group = agonist_name)
) +
  geom_point(size = 2) +
  geom_line(linewidth = 1) +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
  labs(
    title = "Platelet Aggregation Inhibition by Agonist Type",
    subtitle = "Percent inhibition relative to control baseline",
    x = "Ibrutinib Dose (mg)",
    y = "Percent Inhibition (%)",
    color = "Agonist"
  ) +
  scale_color_brewer(palette = "Set3") +
  scale_y_continuous(labels = scales::percent_format(scale = 1)) +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 14, face = "bold"),
    axis.title = element_text(size = 12),
    panel.grid.minor = element_blank()
  )

# Save inhibition plot as PNG
png("comprehensive_inhibition.png", width = 1200, height = 800, res = 150)
print(plot_comprehensive_inhibition)
dev.off()

# 3. Time course comparison for clinical dose (420 mg)
plot_comprehensive_timecourse <- ggplot(
  pd_comprehensive_results %>% filter(dose_mg %in% c(0, 420) & time <= 72), 
  aes(x = time/24, y = Agg, color = agonist_name, linetype = treatment_group)
) +
  geom_line(linewidth = 1) +
  labs(
    title = "Platelet Aggregation Time Course: Control vs Clinical Dose",
    subtitle = "First 3 days of treatment (420 mg ibrutinib)",
    x = "Time (days)",
    y = "Normalized Aggregation",
    color = "Agonist",
    linetype = "Treatment"
  ) +
  scale_color_brewer(palette = "Set3") +
  scale_linetype_manual(values = c("solid", "dashed")) +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 14, face = "bold"),
    axis.title = element_text(size = 12),
    panel.grid.minor = element_blank()
  )

# Save timecourse plot as PNG
png("comprehensive_timecourse.png", width = 1200, height = 800, res = 150)
print(plot_comprehensive_timecourse)
dev.off()

# =============================================================================
# PART 10: RESULTS SUMMARY AND VALIDATION
# =============================================================================

# Summary statistics
cat("\n=== COMPREHENSIVE MODEL RESULTS SUMMARY ===\n\n")

# Control responses by agonist
control_summary <- comprehensive_peak_responses %>%
  filter(dose_mg == 0 & day == 14) %>%
  select(agonist_name, peak_agg) %>%
  arrange(desc(peak_agg))

cat("Control group responses (Day 14):\n")
print(control_summary)

# Clinical dose effects
clinical_dose_summary <- comprehensive_inhibition_data %>%
  filter(dose_mg == 420) %>%
  select(agonist_name, percent_inhibition, peak_agg, control_agg) %>%
  arrange(desc(percent_inhibition))

cat("\nClinical dose (420 mg) effects:\n")
print(clinical_dose_summary)

# Pathway sensitivity analysis
pathway_sensitivity <- comprehensive_inhibition_data %>%
  filter(dose_mg == 420) %>%
  left_join(agonist_data, by = c("agonist_name" = "Agonist")) %>%
  select(agonist_name, `Primary Pathway`, percent_inhibition) %>%
  arrange(desc(percent_inhibition))

cat("\nPathway sensitivity to ibrutinib:\n")
print(pathway_sensitivity)

# Data validation checks
cat("\n=== DATA VALIDATION ===\n")
cat(sprintf("Total agonists simulated: %d\n", length(unique(pd_comprehensive_results$agonist_name))))
cat(sprintf("Total dose levels: %d\n", length(unique(pd_comprehensive_results$dose_mg))))
cat(sprintf("Simulation time points: %d\n", length(unique(pd_comprehensive_results$time))))
cat(sprintf("Total data points: %d\n", nrow(pd_comprehensive_results)))

# Check for any simulation errors
error_check <- pd_comprehensive_results %>%
  group_by(agonist_name, dose_mg) %>%
  summarize(
    any_na = any(is.na(Agg)),
    any_infinite = any(is.infinite(Agg)),
    max_agg = max(Agg, na.rm = TRUE),
    .groups = 'drop'
  ) %>%
  filter(any_na | any_infinite | max_agg > 1.0)

if (nrow(error_check) > 0) {
  cat("\nWARNING: Simulation errors detected:\n")
  print(error_check)
} else {
  cat("\nAll simulations completed successfully with valid results.\n")
}

cat("\n=== COMPREHENSIVE MODEL COMPLETE ===\n")
cat("Comprehensive platelet aggregation model successfully incorporates all agonists\n")
cat("from the CSV file while maintaining compatibility with the existing codebase.\n")