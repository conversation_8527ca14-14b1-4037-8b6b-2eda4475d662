# =============================================================================
# IBRUTINIB DOSE-RESPONSE MODEL FOR PLATELET AGGREGATION - WITH CONTROL GROUP
# =============================================================================
# This updated model includes a control group (0 mg ibrutinib) for proper
# baseline comparison and validation of drug effects
# =============================================================================

# Required packages
library(deSolve)    # For solving ODEs
library(ggplot2)    # For visualization
library(dplyr)      # For data manipulation
library(tidyr)      # For data reshaping
library(patchwork)  # For combining plots
library(RColorBrewer) # For better color palettes
library(svglite)

# Load centralized plotting theme system
source("plotting_theme_system.R")

# =============================================================================
# PART 1: PHARMACOKINETIC (PK) MODEL - UPDATED TO HANDLE CONTROL
# =============================================================================

# Function to run PK simulation for a specific dose (including 0 mg control)
run_pk_simulation <- function(dose_mg) {
  pk_params <- c(
    ka1 = 0.451,        # First-order absorption rate (h^-1)
    ke = 0.1,           # Elimination rate (h^-1)
    Vcentral = 1000,    # Central compartment volume (L)
    F = 0.06            # Bioavailability
  )
  
  # Handle control case (no drug administration)
  if (dose_mg == 0) {
    # For control, create zero concentration profile
    sim_times <- seq(0, 14*24, by = 0.1)
    pk_sim_results <- data.frame(
      time = sim_times, 
      Cp = rep(0, length(sim_times)),
      Cp_nM = rep(0, length(sim_times)),
      dose_mg = 0
    )
    return(pk_sim_results)
  }
  
  # Standard PK simulation for non-zero doses
  state_init <- c(
    Agut = dose_mg * 1000 * unname(pk_params["F"]),
    Acentral = 0
  )
  
  # Pharmacokinetic model function
  pk_model <- function(time, state, parameters) {
    with(as.list(c(state, parameters)), {
      # Differential equations for PK model
      dAgut_dt <- - ka1 * state[1]
      dAcentral_dt <- ka1 * state[1] - ke * state[2]
      
      # Calculate plasma concentration
      Cp <- Acentral / Vcentral
      
      # Return derivatives
      return(list(c(dAgut_dt, dAcentral_dt), Cp = Cp))
    })
  }
  
  # Define dosing regimen once daily for 14 days
  dose_times <- seq(24, 13*24, by = 24)  # Every 24 hours
  
  injectevents <- data.frame(var = "Agut",
                             time = dose_times,
                             value = dose_mg*1000*unname(pk_params["F"]),
                             method = "add")
  
  # Initialize simulation
  sim_times <- seq(0, 14*24, by = 0.1)  # 14 days with 0.1h resolution
  out <- ode(func = pk_model, times = sim_times, y = state_init,
             parms = pk_params,
             events = list(data = injectevents))
  
  pk_sim_results <- data.frame(time = sim_times, Cp = numeric(length(sim_times)))
  pk_sim_results$Cp = out[, "Cp"]
  
  # Convert concentration from μg/L to nM (MW of ibrutinib = 440.5 g/mol)
  pk_sim_results$Cp_nM <- pk_sim_results$Cp / 440.5 * 1000
  pk_sim_results$dose_mg <- dose_mg
  
  return(pk_sim_results)
}

# UPDATED: Define doses to simulate including control (0 mg)
doses <- c(0, 140, 280, 420, 560, 840)  # Added 0 mg as control

# Run PK simulations for each dose including control
pk_results_list <- lapply(doses, run_pk_simulation)
pk_all_results <- bind_rows(pk_results_list)

# =============================================================================
# PART 2: PHARMACODYNAMIC (PD) MODEL - UPDATED TO HANDLE CONTROL
# =============================================================================

# Modified stimuli function to handle individual stimuli (unchanged)
get_stimuli <- function(time, parameters, stimulus_type) {
  with(as.list(parameters), {
    # Default - no stimulus
    collagen <- 0
    ADP <- 0
    thrombin <- 0
    
    # Define when stimulation happens (8 AM each day - 8 hours into each 24 hour period)
    stim_times <- seq(8, 14*24, by = 24)  # stimulation at 8 AM each day
    
    # Check if current time is within activation window for any stimulation event
    for (stim_time in stim_times) {
      # Apply stimulus for a defined duration (30 minutes = 0.5 hours)
      if (time >= stim_time && time < (stim_time + 0.5)) {
        # Use a smoother sigmoid activation function 
        relative_time <- (time - stim_time) / 0.5  # Normalized time from 0 to 1
        scale_factor <- 0.5 * (tanh(10 * (relative_time - 0.5)) + 1)
        
        # Activate only the specified pathway
        if (stimulus_type == "collagen") {
          collagen <- scale_factor * collagen_conc  # μg/mL
        } else if (stimulus_type == "ADP") {
          ADP <- scale_factor * ADP_conc  # μM
        } else if (stimulus_type == "thrombin") {
          thrombin <- scale_factor * thrombin_conc  # U/mL
        }
      }
    }
    
    return(list(collagen = collagen, ADP = ADP, thrombin = thrombin))
  })
}

# Updated PD model with control group handling
pd_model <- function(time, state, parameters, ibrutinib_conc, stimulus_type) {
  with(as.list(c(state, parameters)), {
    # Extract current ibrutinib concentration based on simulation time
    t_idx <- max(1, which.min(abs(time - ibrutinib_conc$time)))
    C_ibr <- ibrutinib_conc$Cp_nM[t_idx]
    
    # Get current stimulus levels based on specified stimulus type
    stimuli <- get_stimuli(time, parameters, stimulus_type)
    collagen <- stimuli$collagen
    ADP <- stimuli$ADP
    thrombin <- stimuli$thrombin
    
    # Avoid division by zero and extreme values
    safeguard <- function(x, min_val = 1e-10, max_val = 1e10) {
      pmax(pmin(x, max_val), min_val)
    }
    
    # Modified Hill function with improved numerical stability
    hill_function <- function(x, EC50, n) {
      x_safe <- safeguard(x)
      EC50_safe <- safeguard(EC50)
      result <- (x_safe/EC50_safe)^n / (1 + (x_safe/EC50_safe)^n)
      safeguard(result, min_val = 0, max_val = 1)
    }
    
    # Four-parameter logistic function for dose-response relationships
    # UPDATED: Handle control case where C_ibr = 0
    dose_response <- function(conc, IC50, hill_coef, max_inhib) {
      if (conc == 0) {
        # Control case: no inhibition
        return(1.0)
      }
      
      conc_safe <- safeguard(conc)
      base_response <- (conc_safe^hill_coef) / (IC50^hill_coef + conc_safe^hill_coef)
      # Scale to maximum inhibition (0 to max_inhib)
      inhibition <- max_inhib * base_response
      # Return fraction remaining (1 - inhibition)
      return(1 - inhibition)
    }
    
    # BTK dynamics with updated dose-response model
    # Different IC50 values for each pathway based on literature
    if (stimulus_type == "collagen") {
      # Collagen pathway: most sensitive to ibrutinib
      Ki_eff <- Ki_collagen
      max_inhib_BTK <- max_inhib_BTK_collagen
      hill_BTK <- hill_BTK_collagen
    } else if (stimulus_type == "ADP") {
      # ADP pathway: moderately sensitive to ibrutinib
      Ki_eff <- Ki_ADP
      max_inhib_BTK <- max_inhib_BTK_ADP
      hill_BTK <- hill_BTK_ADP
    } else {
      # Thrombin pathway: least sensitive to ibrutinib
      Ki_eff <- Ki_thrombin
      max_inhib_BTK <- max_inhib_BTK_thrombin
      hill_BTK <- hill_BTK_thrombin
    }
    
    # Updated BTK dynamics with dose-response relationship
    BTK_free_safe <- safeguard(BTK_free)
    ibr_effect <- dose_response(C_ibr, Ki_eff, hill_BTK, max_inhib_BTK)
    
    # UPDATED: For control case (C_ibr = 0), ibr_effect = 1, so no inhibition occurs
    # Irreversible binding model with dose-dependent inactivation
    dBTK_free_dt <- k_syn - k_inact * (1 - ibr_effect) * BTK_free_safe - k_deg * BTK_free_safe
    dBTK_bound_dt <- k_inact * (1 - ibr_effect) * BTK_free_safe - k_deg * BTK_bound
    
    # Pathway activation - same as before but with updated parameters
    
    # 1. GPVI pathway (Collagen)
    GPVI_activation <- hill_function(collagen, EC50_GPVI, n_GPVI)
    BTK_effect_GPVI <- hill_function(BTK_free_safe, EC50_BTK, n_BTK)
    GPVI_signaling <- GPVI_activation * BTK_effect_GPVI
    
    # 2. P2Y pathway (ADP)
    P2Y_activation <- hill_function(ADP, EC50_P2Y, n_P2Y)
    P2Y_BTK_effect <- hill_function(BTK_free_safe, EC50_BTK_P2Y, 1)
    P2Y_signaling <- P2Y_activation * (R_BTK_P2Y * P2Y_BTK_effect + (1 - R_BTK_P2Y))
    
    # 3. PAR pathway (Thrombin)
    PAR_activation <- hill_function(thrombin, EC50_PAR, n_PAR)
    PAR_BTK_effect <- hill_function(BTK_free_safe, EC50_BTK_PAR, 1)
    PAR_signaling <- PAR_activation * (R_BTK_PAR * PAR_BTK_effect + (1 - R_BTK_PAR))
    
    # Tec kinase contribution with pathway-specific dose-response
    # UPDATED: Handle control case for Tec kinase
    Tec_inhibition <- dose_response(C_ibr, Ki_Tec, hill_Tec, max_inhib_Tec)
    
    # Adjust Tec effect based on stimulus type
    if (stimulus_type == "collagen") {
      Tec_effect <- Tec_inhibition * Tec_contribution * GPVI_activation
    } else if (stimulus_type == "ADP") {
      Tec_effect <- Tec_inhibition * Tec_contribution * P2Y_activation * 0.5
    } else if (stimulus_type == "thrombin") {
      Tec_effect <- Tec_inhibition * Tec_contribution * PAR_activation * 0.3
    } else {
      Tec_effect <- 0
    }
    
    # Total PLCγ2 activation with bounded value
    # Only include the appropriate pathway based on stimulus type
    if (stimulus_type == "collagen") {
      PLC_activation <- k_PLC_act * safeguard(R_GPVI * GPVI_signaling + Tec_effect, min_val = 0, max_val = 1)
    } else if (stimulus_type == "ADP") {
      PLC_activation <- k_PLC_act * safeguard(R_P2Y * P2Y_signaling + Tec_effect, min_val = 0, max_val = 1)
    } else if (stimulus_type == "thrombin") {
      PLC_activation <- k_PLC_act * safeguard(R_PAR * PAR_signaling + Tec_effect, min_val = 0, max_val = 1)
    } else {
      PLC_activation <- 0
    }
    
    dPLCgamma2_act_dt <- PLC_activation - k_PLC_deact * PLCgamma2_act
    
    # Downstream signaling (unchanged)
    Ca_effect <- hill_function(PLCgamma2_act, EC50_Ca, n_Ca)
    dCa_dt <- k_Ca_in * Ca_effect - k_Ca_out * Ca
    
    Int_effect <- hill_function(Ca, EC50_int, n_int)
    dIntegrin_act_dt <- k_int_act * Int_effect - k_int_deact * Integrin_act
    
    dPsel_exp_dt <- k_Psel_exp * safeguard(Ca, min_val = 0, max_val = 1) - k_Psel_int * Psel_exp
    
    # Simplified aggregation with bounded values
    Agg_safe <- safeguard(Agg, min_val = 0, max_val = 0.9999)  # Prevent Agg from reaching 1
    dAgg_dt <- k_agg * safeguard(Integrin_act, min_val = 0, max_val = 1) * (1 - Agg_safe) * 
      (1 + k_coop * Agg_safe) - k_disagg * Agg_safe
    
    return(list(c(
      dBTK_free_dt, dBTK_bound_dt, dPLCgamma2_act_dt, 
      dCa_dt, dIntegrin_act_dt, dPsel_exp_dt, dAgg_dt
    ), GPVI_act = GPVI_activation, P2Y_act = P2Y_activation, PAR_act = PAR_activation,
    Collagen = collagen, ADP = ADP, Thrombin = thrombin, Ibr_conc = C_ibr))
  })
}

# =============================================================================
# PART 3: MODEL PARAMETERS (UNCHANGED)
# =============================================================================

# Set updated PD parameters based on literature values for dose-response
pd_params <- c(
  # BTK parameters
  k_syn = 0.05,            # BTK synthesis rate (nM/h)
  k_deg = 0.035,           # BTK degradation rate (h^-1)
  k_inact = 3.0,           # Ibrutinib-BTK inactivation rate (h^-1)
  
  # Ibrutinib dose-response parameters (pathway-specific based on literature)
  # Collagen/GPVI pathway (most sensitive to ibrutinib)
  Ki_collagen = 150,         # Effective IC50 for collagen pathway (nM)
  max_inhib_BTK_collagen = 0.98, # Maximum inhibition (98%) for collagen pathway
  hill_BTK_collagen = 1.5,    # Hill coefficient for BTK inhibition in collagen pathway
  
  # ADP/P2Y pathway (moderately sensitive)
  Ki_ADP = 400,              # Effective IC50 for ADP pathway (nM)
  max_inhib_BTK_ADP = 0.55,  # Maximum inhibition (55%) for ADP pathway
  hill_BTK_ADP = 1.2,         # Hill coefficient for BTK inhibition in ADP pathway
  
  # Thrombin/PAR pathway (least sensitive)
  Ki_thrombin = 700,          # Effective IC50 for thrombin pathway (nM)
  max_inhib_BTK_thrombin = 0.35, # Maximum inhibition (35%) for thrombin pathway
  hill_BTK_thrombin = 1.0,     # Hill coefficient for BTK inhibition in thrombin pathway
  
  # Tec kinase parameters
  Ki_Tec = 10.0,           # Ibrutinib-Tec binding affinity (nM)
  hill_Tec = 1.2,          # Hill coefficient for Tec kinase inhibition
  max_inhib_Tec = 0.9,     # Maximum inhibition (90%) for Tec kinase
  Tec_contribution = 0.3,  # Relative contribution of Tec to PLCγ2 activation
  
  # Stimulus parameters (concentrations)
  collagen_conc = 5.0,     # Collagen concentration (μg/mL)
  ADP_conc = 10.0,         # ADP concentration (μM)
  thrombin_conc = 0.1,     # Thrombin concentration (U/mL)
  
  # Collagen-GPVI pathway
  EC50_GPVI = 1.0,         # Half-maximal collagen concentration for GPVI activation (μg/mL)
  n_GPVI = 1.5,            # Hill coefficient for GPVI activation
  R_GPVI = 0.7,            # Relative contribution of GPVI pathway to overall platelet activation
  
  # ADP-P2Y pathway
  EC50_P2Y = 1.0,          # Half-maximal ADP concentration for P2Y activation (μM)
  n_P2Y = 1.2,             # Hill coefficient for P2Y activation
  R_P2Y = 0.5,             # Relative contribution of P2Y pathway to overall platelet activation
  R_BTK_P2Y = 0.3,         # Fraction of P2Y signaling dependent on BTK
  EC50_BTK_P2Y = 1.0,      # Half-maximal BTK concentration for P2Y pathway (nM)
  
  # Thrombin-PAR pathway
  EC50_PAR = 0.05,         # Half-maximal thrombin concentration for PAR activation (U/mL)
  n_PAR = 2.0,             # Hill coefficient for PAR activation
  R_PAR = 0.9,             # Relative contribution of PAR pathway to overall platelet activation
  R_BTK_PAR = 0.1,         # Fraction of PAR signaling dependent on BTK
  EC50_BTK_PAR = 1.0,      # Half-maximal BTK concentration for PAR pathway (nM)
  
  # PLCγ2 parameters (unchanged)
  k_PLC_act = 10.0,        # PLCγ2 max activation rate (h^-1)
  k_PLC_deact = 20.0,      # PLCγ2 deactivation rate (h^-1)
  n_BTK = 1.5,             # Hill coefficient for BTK-mediated PLCγ2 activation
  EC50_BTK = 2.0,          # Half-maximal BTK concentration for PLCγ2 activation (nM)
  
  # Calcium parameters (unchanged)
  k_Ca_in = 60.0,          # Max calcium influx rate (h^-1)
  k_Ca_out = 120.0,        # Calcium efflux rate (h^-1)
  n_Ca = 2.0,              # Hill coefficient for calcium signaling
  EC50_Ca = 0.3,           # Half-maximal PLCγ2 activation for calcium response
  
  # Integrin parameters (unchanged)
  k_int_act = 30.0,        # Max integrin activation rate (h^-1)
  k_int_deact = 15.0,      # Integrin deactivation rate (h^-1)
  n_int = 1.5,             # Hill coefficient for integrin activation
  EC50_int = 0.5,          # Half-maximal calcium level for integrin activation
  
  # P-selectin parameters (unchanged)
  k_Psel_exp = 24.0,       # P-selectin expression rate (h^-1)
  k_Psel_int = 6.0,        # P-selectin internalization rate (h^-1)
  
  # Aggregation parameters (unchanged)
  k_agg = 45.0,            # Base aggregation rate (h^-1)
  k_disagg = 1.0,          # Disaggregation rate (h^-1)
  k_coop = 2.0             # Cooperativity coefficient for aggregation
)

# Define initial conditions for the PD model (unchanged)
pd_state_init <- c(
  BTK_free = 5.0,         # Initial free BTK (nM)
  BTK_bound = 0.0,        # Initial bound BTK (nM)
  PLCgamma2_act = 0.0,    # Initial activated PLCγ2 (normalized)
  Ca = 0.0,               # Initial calcium (normalized)
  Integrin_act = 0.0,     # Initial activated integrin (normalized)
  Psel_exp = 0.0,         # Initial P-selectin expression (normalized)
  Agg = 0.0               # Initial aggregation (normalized)
)

# =============================================================================
# PART 4: SIMULATION EXECUTION
# =============================================================================

# Define simulation time points
pd_sim_times <- seq(0, 14*24, by = 0.1)

# Define the different stimulus types to evaluate
stimulus_types <- c("collagen", "ADP", "thrombin")

# Function to run a single PD simulation with given dose and stimulus type
run_pd_simulation <- function(dose_mg, stimulus_type) {
  # Get PK profile for this dose (includes control case)
  pk_data <- pk_results_list[[which(doses == dose_mg)]]
  
  # Run PD model simulation
  pd_out <- ode(
    y = pd_state_init,
    times = pd_sim_times,
    func = pd_model,
    parms = pd_params,
    ibrutinib_conc = pk_data,
    stimulus_type = stimulus_type,
    method = "lsoda",
    rtol = 1e-2,    # Relaxed relative tolerance
    atol = 1e-2,    # Relaxed absolute tolerance
    hmin = 1e-10,   # Minimum step size
    maxsteps = 50000 # Increase maximum steps
  )
  
  # Convert to data frame for analysis
  pd_results <- as.data.frame(pd_out)
  names_vector <- c("time", "BTK_free", "BTK_bound", "PLCgamma2_act", 
                    "Ca", "Integrin_act", "Psel_exp", "Agg")
  colnames(pd_results)[1:length(names_vector)] <- names_vector
  
  # Extract additional data
  pd_results$GPVI_activation <- pd_out[,"GPVI_act"]
  pd_results$P2Y_activation <- pd_out[,"P2Y_act"]
  pd_results$PAR_activation <- pd_out[,"PAR_act"]
  pd_results$Collagen <- pd_out[,"Collagen"]
  pd_results$ADP <- pd_out[,"ADP"]
  pd_results$Thrombin <- pd_out[,"Thrombin"]
  pd_results$Ibr_conc <- pd_out[,"Ibr_conc"]
  
  # Add metadata
  pd_results$stimulus_type <- stimulus_type
  pd_results$dose_mg <- dose_mg
  
  # Add treatment group label for easier analysis
  pd_results$treatment_group <- ifelse(dose_mg == 0, "Control", paste0(dose_mg, " mg"))
  
  return(pd_results)
}

# Create nested lists to store results for all combinations
all_results <- list()

# Run simulations for all combinations of dose and stimulus
for (stim_type in stimulus_types) {
  stim_results <- list()
  for (dose in doses) {
    stim_results[[as.character(dose)]] <- run_pd_simulation(dose, stim_type)
  }
  all_results[[stim_type]] <- stim_results
}

# Combine all results into a single data frame
pd_all_results <- bind_rows(lapply(all_results, function(stim_list) {
  bind_rows(stim_list)
}))

# =============================================================================
# PART 5: UPDATED DATA ANALYSIS INCLUDING CONTROL
# =============================================================================

# Extract peak aggregation responses for each stimulus-dose combination
extract_peak_responses <- function(data) {
  # Group data by stimulus type, dose, and day
  grouped_data <- data %>%
    mutate(day = floor(time/24) + 1) %>%
    group_by(stimulus_type, dose_mg, day, treatment_group) %>%
    summarize(peak_agg = max(Agg), 
              peak_integrin = max(Integrin_act),
              peak_plc = max(PLCgamma2_act),
              avg_BTK_free = mean(BTK_free),
              max_ibr_conc = max(Ibr_conc),
              .groups = 'drop')
  
  return(grouped_data)
}

# Get peak responses including control
peak_responses <- extract_peak_responses(pd_all_results)

# Create dose-response curves for day 14 (steady state) including control
day14_responses <- peak_responses %>%
  filter(day == 14) %>%
  dplyr::select(stimulus_type, dose_mg, treatment_group, peak_agg, peak_integrin, peak_plc) %>%
  pivot_longer(cols = c(peak_agg, peak_integrin, peak_plc),
               names_to = "endpoint", values_to = "response")

# UPDATED: Calculate percent inhibition relative to control
calculate_ic50_data_with_control <- function() {
  # Get day 14 data for dose-response calculation
  day14_data <- peak_responses %>%
    filter(day == 14)
  
  # Get control response (0 mg dose) for each stimulus type
  control_responses <- day14_data %>%
    filter(dose_mg == 0) %>%
    dplyr::select(stimulus_type, control_agg = peak_agg)
  
  # Calculate percent inhibition relative to control
  ic50_data <- day14_data %>%
    left_join(control_responses, by = "stimulus_type") %>%
    mutate(percent_inhibition = 100 * (1 - peak_agg/control_agg)) %>%
    dplyr::select(stimulus_type, dose_mg, treatment_group, percent_inhibition)
  
  return(ic50_data)
}

ic50_data <- calculate_ic50_data_with_control()

# UPDATED: Enhanced bleeding risk prediction including control comparison
predict_bleeding_risk_with_control <- function(collagen_inhib, adp_inhib, thrombin_inhib) {
  # Weights for each pathway's contribution to bleeding risk
  w_collagen <- 0.6
  w_adp <- 0.3
  w_thrombin <- 0.1
  
  # Calculate weighted risk score (0-100 scale)
  risk_score <- w_collagen * collagen_inhib + 
    w_adp * adp_inhib + 
    w_thrombin * thrombin_inhib
  
  return(risk_score)
}

# Calculate predicted bleeding risk for each dose including control
bleeding_risk_with_control <- day14_responses %>%
  filter(endpoint == "peak_agg") %>%
  dplyr::select(stimulus_type, dose_mg, treatment_group, response) %>%
  pivot_wider(names_from = stimulus_type, values_from = response) %>%
  # Calculate control baseline for each pathway
  mutate(
    # Get control values
    control_collagen = collagen[dose_mg == 0],
    control_adp = ADP[dose_mg == 0],
    control_thrombin = thrombin[dose_mg == 0],
    # Calculate inhibition relative to control
    collagen_inhib = 100 * (1 - collagen/control_collagen),
    adp_inhib = 100 * (1 - ADP/control_adp),
    thrombin_inhib = 100 * (1 - thrombin/control_thrombin),
    # Calculate bleeding risk
    bleeding_risk = predict_bleeding_risk_with_control(collagen_inhib, adp_inhib, thrombin_inhib)
  )

# =============================================================================
# PART 6: UPDATED VISUALIZATION INCLUDING CONTROL
# =============================================================================

# 1. UPDATED: PK profile including control (shows zero concentration)
plot_pk_all_with_control <- ggplot(pk_all_results, aes(x = time/24, y = Cp_nM, color = factor(dose_mg))) +
  geom_line(linewidth = 1.2, alpha = 0.8) +
  labs(
    title = "Ibrutinib Pharmacokinetic Profiles Across Dose Range",
    subtitle = "Plasma concentration over time including control (0 mg) baseline",
    x = "Time (days)",
    y = "Plasma Concentration (nM)",
    color = "Dose (mg)",
    caption = "Control group (0 mg) shows no drug exposure; Higher doses show proportional increases in exposure"
  ) +
  scale_color_ibrutinib(type = "categorical") +
  scale_x_continuous(breaks = seq(0, 14, by = 2)) +
  scale_y_continuous(labels = scales::comma_format()) +
  theme_ibrutinib_base() +
  theme(legend.position = "right")

print(plot_pk_all_with_control)

# 2. UPDATED: Dose-response curves including control baseline
plot_dose_response_with_control <- ggplot(
  day14_responses %>% filter(endpoint == "peak_agg"), 
  aes(x = dose_mg, y = response, color = stimulus_type, group = stimulus_type)
) +
  geom_point(size = 3) +
  geom_line(linewidth = 1) +
  # Highlight the control point
  geom_point(data = day14_responses %>% filter(endpoint == "peak_agg" & dose_mg == 0),
             aes(x = dose_mg, y = response), size = 5, shape = 21, 
             stroke = 2, fill = "white") +
  annotate("text", x = 50, y = 0.8, label = "Control\n(No Drug)", hjust = 0) +
  labs(
    title = "Platelet Aggregation Response by Dose and Stimulus Type",
    subtitle = "Day 14 responses showing differential sensitivity across pathways",
    x = "Ibrutinib Dose (mg)",
    y = "Peak Aggregation Response",
    color = "Stimulus Type",
    caption = "Control (0 mg) represents normal platelet function baseline"
  ) +
  scale_color_ibrutinib(type = "categorical") +
  scale_x_continuous(breaks = create_dose_breaks(day14_responses$dose_mg)) +
  scale_y_continuous(labels = scales::number_format(accuracy = 0.1)) +
  theme_ibrutinib_base() +
  theme(legend.position = "right")

print(plot_dose_response_with_control)

# 3. UPDATED: Percent inhibition vs dose (now properly calculated relative to control)
plot_percent_inhibition_with_control <- ggplot(
  ic50_data %>% filter(dose_mg > 0), # Exclude control from inhibition plot
  aes(x = dose_mg, y = percent_inhibition, color = stimulus_type, group = stimulus_type)
) +
  geom_point(size = 3) +
  geom_line(linewidth = 1) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black", alpha = 0.5) +
  labs(
    title = "Platelet Aggregation Inhibition by Dose",
    subtitle = "Percent inhibition relative to control baseline across pathways",
    x = "Ibrutinib Dose (mg)",
    y = "Percent Inhibition Relative to Control (%)",
    color = "Stimulus Type",
    caption = "Collagen pathway shows highest sensitivity to ibrutinib inhibition"
  ) +
  scale_color_ibrutinib(type = "categorical") +
  scale_x_continuous(breaks = create_dose_breaks(ic50_data$dose_mg[ic50_data$dose_mg > 0])) +
  scale_y_continuous(labels = scales::percent_format(scale = 1),
                     breaks = seq(0, 100, by = 20)) +
  theme_ibrutinib_base() +
  theme(legend.position = "right")

print(plot_percent_inhibition_with_control)

# 4. NEW: Control vs Treatment Comparison for Clinical Dose
plot_control_vs_treatment <- ggplot(
  pd_all_results %>% filter(dose_mg %in% c(0, 420) & time <= 72), # First 3 days
  aes(x = time/24, y = Agg, color = stimulus_type, linetype = treatment_group)
) +
  geom_line(linewidth = 1) +
  labs(
    title = "Platelet Aggregation: Control vs Clinical Dose (420 mg)",
    subtitle = "First 3 days of treatment",
    x = "Time (days)",
    y = "Normalized Aggregation",
    color = "Stimulus Type",
    linetype = "Treatment"
  ) +
  scale_color_brewer(palette = "Set1") +
  scale_linetype_manual(values = c("solid", "dashed")) +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 16, face = "bold"),
    axis.title = element_text(size = 14),
    axis.text = element_text(size = 12),
    panel.grid = element_blank(),
    axis.line = element_line(color = "black"),
    axis.ticks = element_line(color = "black")
  )

print(plot_control_vs_treatment)

# 5. UPDATED: Bleeding risk prediction including control baseline
plot_bleeding_risk_with_control <- ggplot(
  bleeding_risk_with_control %>% filter(dose_mg > 0), # Exclude control
  aes(x = dose_mg, y = bleeding_risk)
) +
  geom_point(size = 3, color = "red") +
  geom_line(linewidth = 1, color = "red") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black", alpha = 0.5) +
  annotate("text", x = 200, y = 5, label = "Control baseline = 0%", hjust = 0) +
  labs(
    title = "Predicted Bleeding Risk vs Ibrutinib Dose\n(Relative to Control Baseline)",
    x = "Ibrutinib Dose (mg)",
    y = "Predicted Bleeding Risk Score (%)"
  ) +
  theme_minimal() +
  theme(
    panel.grid = element_blank(),
    axis.line = element_line(color = "black"),
    axis.text = element_text(color = "black", size = 12),
    axis.title = element_text(color = "black", size = 14, face = "bold"),
    plot.title = element_text(size = 16, face = "bold"),
    axis.ticks = element_line(color = "black")
  )

print(plot_bleeding_risk_with_control)

# 6. NEW: Summary table comparing control vs treatments
control_vs_treatment_summary <- peak_responses %>%
  filter(day == 14) %>%
  group_by(stimulus_type) %>%
  summarize(
    control_response = peak_agg[dose_mg == 0],
    clinical_dose_response = peak_agg[dose_mg == 420],
    percent_inhibition = 100 * (1 - clinical_dose_response/control_response),
    .groups = 'drop'
  )

print("=== CONTROL VS CLINICAL DOSE COMPARISON ===")
print(control_vs_treatment_summary)

# 7. NEW: Validation plot showing normal platelet function (control)
plot_control_validation <- ggplot(
  pd_all_results %>% filter(dose_mg == 0 & time <= 72), # Control only, first 3 days
  aes(x = time/24, y = Agg, color = stimulus_type)
) +
  geom_line(linewidth = 1) +
  labs(
    title = "Normal Platelet Aggregation Response (Control - No Drug)",
    subtitle = "Baseline platelet function in response to different stimuli",
    x = "Time (days)",
    y = "Normalized Aggregation",
    color = "Stimulus Type"
  ) +
  scale_color_brewer(palette = "Set1") +
  theme_minimal() +
  theme(
    legend.position = "right",
    plot.title = element_text(size = 16, face = "bold"),
    axis.title = element_text(size = 14),
    axis.text = element_text(size = 12),
    panel.grid = element_blank(),
    axis.line = element_line(color = "black"),
    axis.ticks = element_line(color = "black")
  )

print(plot_control_validation)

# =============================================================================
# PART 7: CONCLUSION AND SUMMARY WITH CONTROL
# =============================================================================

cat("=== MODEL VALIDATION WITH CONTROL GROUP ===\n\n")

cat("Control group aggregation responses (Day 14):\n")
control_summary <- peak_responses %>%
  filter(dose_mg == 0 & day == 14) %>%
  dplyr::select(stimulus_type, peak_agg) %>%
  arrange(desc(peak_agg))
print(control_summary)

cat("\nClinical dose (420 mg) effects relative to control:\n")
print(control_vs_treatment_summary)

cat("\nKey findings with control validation:\n")
cat("1. Control group shows normal platelet aggregation responses\n")
cat("2. Thrombin shows highest baseline aggregation in control\n")
cat("3. Collagen pathway most sensitive to ibrutinib (up to 98% inhibition)\n")
cat("4. ADP and thrombin pathways show minimal inhibition even at high doses\n")
cat("5. Bleeding risk calculation properly accounts for baseline function\n")

# Save key results including control
bleeding_risk_summary <- bleeding_risk_with_control %>%
  dplyr::select(dose_mg, treatment_group, bleeding_risk) %>%
  filter(dose_mg %in% c(0, 140, 420, 840))

cat("\nBleeding risk by dose (including control):\n")
print(bleeding_risk_summary)
